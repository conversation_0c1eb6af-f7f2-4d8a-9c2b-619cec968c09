#!/usr/bin/env python3
"""
Main training script for coding models
Supports fine-tuning existing models and training from scratch
"""

import os
import json
import torch
import wandb
from pathlib import Path
from dataclasses import dataclass, field
from typing import Optional

from transformers import (
    AutoTokenizer, AutoModelForCausalLM, AutoConfig,
    TrainingArguments, Trainer, DataCollatorForLanguageModeling,
    EarlyStoppingCallback, get_linear_schedule_with_warmup
)
from datasets import load_from_disk, Dataset
import evaluate
from torch.utils.data import DataLoader

@dataclass
class ModelArguments:
    """Arguments for model configuration"""
    model_name_or_path: str = field(
        default="microsoft/CodeBERT-base",
        metadata={"help": "Path to pretrained model or model identifier"}
    )
    config_name: Optional[str] = field(
        default=None,
        metadata={"help": "Pretrained config name or path"}
    )
    tokenizer_name: Optional[str] = field(
        default=None,
        metadata={"help": "Pretrained tokenizer name or path"}
    )
    cache_dir: Optional[str] = field(
        default=None,
        metadata={"help": "Cache directory for models"}
    )
    model_revision: str = field(
        default="main",
        metadata={"help": "Model revision to use"}
    )

@dataclass
class DataArguments:
    """Arguments for data configuration"""
    dataset_path: str = field(
        default="./data/datasets/code_completion",
        metadata={"help": "Path to the training dataset"}
    )
    max_length: int = field(
        default=512,
        metadata={"help": "Maximum sequence length"}
    )
    preprocessing_num_workers: Optional[int] = field(
        default=None,
        metadata={"help": "Number of processes for preprocessing"}
    )

class CodingModelTrainer:
    def __init__(self, model_args: ModelArguments, data_args: DataArguments, training_args: TrainingArguments):
        self.model_args = model_args
        self.data_args = data_args
        self.training_args = training_args
        
        # Initialize tokenizer and model
        self.tokenizer = None
        self.model = None
        self.datasets = None
        
    def setup_tokenizer(self):
        """Initialize tokenizer"""
        print("🔤 Setting up tokenizer...")
        
        tokenizer_name = self.model_args.tokenizer_name or self.model_args.model_name_or_path
        
        self.tokenizer = AutoTokenizer.from_pretrained(
            tokenizer_name,
            cache_dir=self.model_args.cache_dir,
            revision=self.model_args.model_revision,
        )
        
        # Add special tokens for code
        special_tokens = {
            "pad_token": "<pad>",
            "eos_token": "</s>",
            "bos_token": "<s>",
            "unk_token": "<unk>",
        }
        
        # Add code-specific tokens
        additional_tokens = [
            "<CODE>", "</CODE>",
            "<COMMENT>", "</COMMENT>",
            "<FUNCTION>", "</FUNCTION>",
            "<CLASS>", "</CLASS>"
        ]
        
        # Set special tokens
        for key, token in special_tokens.items():
            if getattr(self.tokenizer, key) is None:
                setattr(self.tokenizer, key, token)
        
        # Add additional tokens
        self.tokenizer.add_tokens(additional_tokens)
        
        print(f"✅ Tokenizer setup complete. Vocab size: {len(self.tokenizer)}")
    
    def setup_model(self):
        """Initialize model"""
        print("🤖 Setting up model...")
        
        config = AutoConfig.from_pretrained(
            self.model_args.config_name or self.model_args.model_name_or_path,
            cache_dir=self.model_args.cache_dir,
            revision=self.model_args.model_revision,
        )
        
        # Adjust config for code generation
        config.vocab_size = len(self.tokenizer)
        config.pad_token_id = self.tokenizer.pad_token_id
        config.eos_token_id = self.tokenizer.eos_token_id
        config.bos_token_id = self.tokenizer.bos_token_id
        
        self.model = AutoModelForCausalLM.from_pretrained(
            self.model_args.model_name_or_path,
            config=config,
            cache_dir=self.model_args.cache_dir,
            revision=self.model_args.model_revision,
        )
        
        # Resize token embeddings to match tokenizer
        self.model.resize_token_embeddings(len(self.tokenizer))
        
        print(f"✅ Model setup complete. Parameters: {self.model.num_parameters():,}")
    
    def load_datasets(self):
        """Load and preprocess datasets"""
        print("📊 Loading datasets...")
        
        try:
            # Load dataset from disk
            self.datasets = load_from_disk(self.data_args.dataset_path)
            print(f"✅ Loaded dataset from {self.data_args.dataset_path}")
            print(f"   Train samples: {len(self.datasets['train'])}")
            print(f"   Validation samples: {len(self.datasets['validation'])}")
            
        except Exception as e:
            print(f"❌ Failed to load dataset: {e}")
            print("Creating sample dataset...")
            self.create_sample_dataset()
    
    def create_sample_dataset(self):
        """Create a small sample dataset for testing"""
        sample_data = [
            {
                "input": "def fibonacci(n):",
                "target": "def fibonacci(n):\n    if n <= 1:\n        return n\n    return fibonacci(n-1) + fibonacci(n-2)"
            },
            {
                "input": "class Calculator:",
                "target": "class Calculator:\n    def add(self, a, b):\n        return a + b\n    def subtract(self, a, b):\n        return a - b"
            }
        ] * 100  # Repeat for more samples
        
        train_dataset = Dataset.from_list(sample_data[:160])
        val_dataset = Dataset.from_list(sample_data[160:])
        
        from datasets import DatasetDict
        self.datasets = DatasetDict({
            "train": train_dataset,
            "validation": val_dataset
        })
        
        print("✅ Created sample dataset for testing")
    
    def preprocess_function(self, examples):
        """Preprocess examples for training"""
        # Combine input and target for language modeling
        texts = []
        for inp, target in zip(examples["input"], examples["target"]):
            text = f"{inp}{target}{self.tokenizer.eos_token}"
            texts.append(text)
        
        # Tokenize
        tokenized = self.tokenizer(
            texts,
            truncation=True,
            padding=False,
            max_length=self.data_args.max_length,
            return_tensors=None,
        )
        
        # For causal LM, labels are the same as input_ids
        tokenized["labels"] = tokenized["input_ids"].copy()
        
        return tokenized
    
    def compute_metrics(self, eval_pred):
        """Compute evaluation metrics"""
        predictions, labels = eval_pred
        
        # Calculate perplexity
        predictions = torch.tensor(predictions)
        labels = torch.tensor(labels)
        
        # Shift predictions and labels for next token prediction
        shift_preds = predictions[..., :-1, :].contiguous()
        shift_labels = labels[..., 1:].contiguous()
        
        # Calculate loss
        loss_fct = torch.nn.CrossEntropyLoss(ignore_index=-100)
        loss = loss_fct(shift_preds.view(-1, shift_preds.size(-1)), shift_labels.view(-1))
        
        perplexity = torch.exp(loss)
        
        return {
            "perplexity": perplexity.item(),
            "eval_loss": loss.item()
        }
    
    def train(self):
        """Main training function"""
        print("🚀 Starting training...")
        
        # Setup components
        self.setup_tokenizer()
        self.setup_model()
        self.load_datasets()
        
        # Preprocess datasets
        print("🔄 Preprocessing datasets...")
        tokenized_datasets = self.datasets.map(
            self.preprocess_function,
            batched=True,
            num_proc=self.data_args.preprocessing_num_workers,
            remove_columns=self.datasets["train"].column_names,
        )
        
        # Data collator
        data_collator = DataCollatorForLanguageModeling(
            tokenizer=self.tokenizer,
            mlm=False,  # We're doing causal LM, not masked LM
        )
        
        # Initialize trainer
        trainer = Trainer(
            model=self.model,
            args=self.training_args,
            train_dataset=tokenized_datasets["train"],
            eval_dataset=tokenized_datasets["validation"],
            tokenizer=self.tokenizer,
            data_collator=data_collator,
            compute_metrics=self.compute_metrics,
            callbacks=[EarlyStoppingCallback(early_stopping_patience=3)]
        )
        
        # Train
        print("🏃‍♂️ Training started...")
        train_result = trainer.train()
        
        # Save model
        trainer.save_model()
        trainer.save_state()
        
        # Log results
        print("✅ Training completed!")
        print(f"Final train loss: {train_result.training_loss:.4f}")
        
        # Evaluate
        eval_results = trainer.evaluate()
        print(f"Final eval loss: {eval_results['eval_loss']:.4f}")
        print(f"Perplexity: {eval_results['perplexity']:.2f}")
        
        return trainer

def main():
    """Main function"""
    print("🎯 Starting Coding Model Training")
    print("=" * 50)
    
    # Configuration
    model_args = ModelArguments(
        model_name_or_path="microsoft/CodeBERT-base"
    )
    
    data_args = DataArguments(
        dataset_path="./data/datasets/code_completion",
        max_length=512
    )
    
    training_args = TrainingArguments(
        output_dir="./models/checkpoints",
        overwrite_output_dir=True,
        num_train_epochs=3,
        per_device_train_batch_size=4,
        per_device_eval_batch_size=4,
        gradient_accumulation_steps=2,
        learning_rate=2e-5,
        weight_decay=0.01,
        warmup_steps=500,
        logging_steps=100,
        save_steps=1000,
        eval_steps=1000,
        evaluation_strategy="steps",
        save_strategy="steps",
        load_best_model_at_end=True,
        metric_for_best_model="eval_loss",
        greater_is_better=False,
        fp16=torch.cuda.is_available(),
        dataloader_num_workers=4,
        remove_unused_columns=False,
        report_to=None,  # Disable wandb for now
    )
    
    # Initialize trainer
    trainer_instance = CodingModelTrainer(model_args, data_args, training_args)
    
    # Train
    trainer = trainer_instance.train()
    
    print("\n🎉 Training pipeline completed!")
    print("Next steps:")
    print("1. Test your model with: python test_model.py")
    print("2. Deploy with: python deploy_model.py")

if __name__ == "__main__":
    main()
