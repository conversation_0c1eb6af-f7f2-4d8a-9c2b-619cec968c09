# AI Coding Assistant Setup

This setup provides you with a local AI coding assistant that runs entirely on your machine using Ollama.

## Quick Start

### 1. Install the AI System
Run the setup script to install Ollama and download coding models:
```powershell
.\setup_ai_coding_assistant.ps1
```

### 2. Start Using the AI Assistant
Once installed, you can use the AI helper in several ways:

#### Command Line Interface
```bash
# Generate code from description
.\ai.bat generate "Python function to calculate fibonacci numbers"

# Explain code in a file
.\ai.bat explain mycode.py

# Review code for bugs and improvements
.\ai.bat review app.js

# Complete partial code
.\ai.bat complete "def fibonacci(n):"

# Get debugging help
.\ai.bat debug "Getting IndexError in my Python list"

# Start interactive chat session
.\ai.bat chat
```

#### Direct Ollama Commands
```bash
# List installed models
ollama list

# Start interactive session with specific model
ollama run deepseek-coder:6.7b

# Start interactive session with Code Llama
ollama run codellama:7b
```

## Available Models

After setup, you'll have these coding-focused AI models:

- **deepseek-coder:6.7b** - Excellent for code generation, completion, and debugging
- **codellama:7b** - <PERSON><PERSON>'s specialized coding model, great for explanations

## Features

### Code Generation
Ask the AI to generate code from natural language descriptions:
```
"Create a REST API endpoint in Python using Flask"
"Write a JavaScript function to validate email addresses"
"Generate a SQL query to find top 10 customers by sales"
```

### Code Explanation
Get detailed explanations of existing code:
```
.\ai.bat explain complex_algorithm.py
```

### Code Review
Get suggestions for improvements, bug fixes, and best practices:
```
.\ai.bat review my_project.js
```

### Interactive Debugging
Get help with errors and debugging:
```
.\ai.bat debug "My React component won't re-render when state changes"
```

### Interactive Chat
Start a conversation with the AI for complex coding discussions:
```
.\ai.bat chat
```

## Example Workflows

### Creating a New Project
1. Describe your project: `.\ai.bat generate "Python web scraper for news articles"`
2. Review the generated code: `.\ai.bat review scraper.py`
3. Get explanations: `.\ai.bat explain scraper.py`
4. Debug issues: `.\ai.bat debug "Getting 403 error when scraping"`

### Learning New Concepts
1. Start chat mode: `.\ai.bat chat`
2. Ask: "Explain async/await in JavaScript with examples"
3. Request practice exercises: "Give me 3 coding exercises to practice async/await"

## Tips for Best Results

1. **Be Specific**: Instead of "fix my code", say "my Python function returns None instead of a list"
2. **Provide Context**: Include relevant code snippets and error messages
3. **Ask Follow-ups**: The AI can explain its suggestions in more detail
4. **Use Different Models**: Try both deepseek-coder and codellama for different perspectives

## Troubleshooting

### Ollama Not Found
If you get "ollama command not found":
1. Restart your terminal
2. Check if Ollama is installed: `ollama --version`
3. If not installed, run the setup script again

### Model Download Issues
If model downloads fail:
1. Check your internet connection
2. Try downloading one model at a time: `ollama pull deepseek-coder:6.7b`
3. For slower connections, try smaller models: `ollama pull codellama:7b`

### Performance Issues
- Larger models (13b, 34b) provide better results but need more RAM
- Smaller models (7b) are faster but less capable
- Close other applications to free up memory for the AI

## Advanced Usage

### Custom Prompts
You can create custom prompts for specific tasks:
```powershell
ollama run deepseek-coder:6.7b "Convert this Python code to JavaScript: [your code here]"
```

### Model Management
```bash
# List all available models online
ollama list

# Remove a model to save space
ollama rm codellama:7b

# Update a model
ollama pull deepseek-coder:6.7b
```

## Privacy and Security

- All AI processing happens locally on your machine
- No code is sent to external servers
- Models work offline after initial download
- Your code remains private and secure

Enjoy your new AI coding assistant! 🚀
