# AI Code Helper Script
# Provides easy commands for AI-assisted coding

param(
    [Parameter(Position=0)]
    [string]$Action,
    
    [Parameter(Position=1)]
    [string]$Input,
    
    [Parameter()]
    [string]$Model = "deepseek-coder:6.7b",
    
    [Parameter()]
    [string]$File,
    
    [Parameter()]
    [switch]$Help
)

function Show-Help {
    Write-Host "AI Code Helper - Terminal-based AI coding assistant" -ForegroundColor Green
    Write-Host ""
    Write-Host "Usage:" -ForegroundColor Yellow
    Write-Host "  .\ai_code_helper.ps1 <action> [options]" -ForegroundColor White
    Write-Host ""
    Write-Host "Actions:" -ForegroundColor Yellow
    Write-Host "  generate <description>     - Generate code from description" -ForegroundColor White
    Write-Host "  explain <file>            - Explain code in a file" -ForegroundColor White
    Write-Host "  review <file>             - Review code for bugs and improvements" -ForegroundColor White
    Write-Host "  complete <partial_code>   - Complete partial code" -ForegroundColor White
    Write-Host "  debug <error_description> - Help debug an error" -ForegroundColor White
    Write-Host "  chat                      - Start interactive chat session" -ForegroundColor White
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Yellow
    Write-Host "  -Model <model_name>       - Specify AI model (default: deepseek-coder:6.7b)" -ForegroundColor White
    Write-Host "  -File <file_path>         - Specify file for file-based actions" -ForegroundColor White
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\ai_code_helper.ps1 generate 'Python function to sort a list'" -ForegroundColor White
    Write-Host "  .\ai_code_helper.ps1 explain -File 'script.py'" -ForegroundColor White
    Write-Host "  .\ai_code_helper.ps1 review -File 'app.js'" -ForegroundColor White
    Write-Host "  .\ai_code_helper.ps1 chat" -ForegroundColor White
}

function Invoke-AICommand {
    param([string]$Prompt, [string]$ModelName = $Model)
    
    try {
        Write-Host "Thinking..." -ForegroundColor Blue
        $result = ollama run $ModelName $Prompt
        return $result
    } catch {
        Write-Host "Error communicating with AI model. Make sure Ollama is running and the model is installed." -ForegroundColor Red
        return $null
    }
}

if ($Help -or -not $Action) {
    Show-Help
    exit
}

switch ($Action.ToLower()) {
    "generate" {
        if (-not $Input) {
            Write-Host "Please provide a description of what code to generate." -ForegroundColor Red
            exit 1
        }
        $prompt = "Generate code for: $Input. Provide clean, well-commented code with explanations."
        $result = Invoke-AICommand -Prompt $prompt
        if ($result) {
            Write-Host "`nGenerated Code:" -ForegroundColor Green
            Write-Host $result
        }
    }
    
    "explain" {
        $targetFile = if ($File) { $File } else { $Input }
        if (-not $targetFile -or -not (Test-Path $targetFile)) {
            Write-Host "Please provide a valid file path." -ForegroundColor Red
            exit 1
        }
        $code = Get-Content $targetFile -Raw
        $prompt = "Explain this code in detail, including what it does, how it works, and any notable patterns or techniques used:`n`n$code"
        $result = Invoke-AICommand -Prompt $prompt
        if ($result) {
            Write-Host "`nCode Explanation:" -ForegroundColor Green
            Write-Host $result
        }
    }
    
    "review" {
        $targetFile = if ($File) { $File } else { $Input }
        if (-not $targetFile -or -not (Test-Path $targetFile)) {
            Write-Host "Please provide a valid file path." -ForegroundColor Red
            exit 1
        }
        $code = Get-Content $targetFile -Raw
        $prompt = "Review this code for bugs, performance issues, security vulnerabilities, and suggest improvements:`n`n$code"
        $result = Invoke-AICommand -Prompt $prompt
        if ($result) {
            Write-Host "`nCode Review:" -ForegroundColor Green
            Write-Host $result
        }
    }
    
    "complete" {
        if (-not $Input) {
            Write-Host "Please provide partial code to complete." -ForegroundColor Red
            exit 1
        }
        $prompt = "Complete this code and explain what you added:`n`n$Input"
        $result = Invoke-AICommand -Prompt $prompt
        if ($result) {
            Write-Host "`nCompleted Code:" -ForegroundColor Green
            Write-Host $result
        }
    }
    
    "debug" {
        if (-not $Input) {
            Write-Host "Please describe the error or issue you're facing." -ForegroundColor Red
            exit 1
        }
        $prompt = "Help debug this issue: $Input. Provide potential causes and solutions."
        $result = Invoke-AICommand -Prompt $prompt
        if ($result) {
            Write-Host "`nDebugging Help:" -ForegroundColor Green
            Write-Host $result
        }
    }
    
    "chat" {
        Write-Host "Starting interactive AI coding session..." -ForegroundColor Green
        Write-Host "Type 'exit' to quit, 'help' for commands" -ForegroundColor Yellow
        Write-Host ""
        
        while ($true) {
            $userInput = Read-Host "You"
            if ($userInput -eq "exit") { break }
            if ($userInput -eq "help") {
                Write-Host "Available commands in chat mode:" -ForegroundColor Yellow
                Write-Host "  - Ask any coding question" -ForegroundColor White
                Write-Host "  - Request code generation" -ForegroundColor White
                Write-Host "  - Ask for explanations" -ForegroundColor White
                Write-Host "  - Get debugging help" -ForegroundColor White
                Write-Host "  - Type 'exit' to quit" -ForegroundColor White
                continue
            }
            
            $result = Invoke-AICommand -Prompt $userInput
            if ($result) {
                Write-Host "AI: $result" -ForegroundColor Cyan
            }
            Write-Host ""
        }
    }
    
    default {
        Write-Host "Unknown action: $Action" -ForegroundColor Red
        Write-Host "Use -Help to see available actions." -ForegroundColor Yellow
    }
}
