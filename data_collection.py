#!/usr/bin/env python3
"""
Data collection and preparation script for coding model training
Collects code from various sources and prepares it for training
"""

import os
import json
import pandas as pd
from datasets import Dataset, DatasetDict, load_dataset
from transformers import AutoTokenizer
import requests
import zipfile
import shutil
from pathlib import Path
import subprocess
from tqdm import tqdm

class CodeDataCollector:
    def __init__(self, data_dir="./data"):
        self.data_dir = Path(data_dir)
        self.raw_dir = self.data_dir / "raw"
        self.processed_dir = self.data_dir / "processed"
        self.datasets_dir = self.data_dir / "datasets"
        
        # Create directories
        for dir_path in [self.raw_dir, self.processed_dir, self.datasets_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def download_public_datasets(self):
        """Download popular coding datasets"""
        print("📥 Downloading public coding datasets...")
        
        datasets_to_download = [
            {
                "name": "code_search_net",
                "config": "python",
                "description": "CodeSearchNet Python dataset"
            },
            {
                "name": "codeparrot/github-code",
                "config": "python",
                "description": "GitHub Python code"
            }
        ]
        
        downloaded_datasets = {}
        
        for dataset_info in datasets_to_download:
            try:
                print(f"Downloading {dataset_info['description']}...")
                dataset = load_dataset(
                    dataset_info["name"], 
                    dataset_info.get("config"),
                    split="train[:10000]"  # Limit size for demo
                )
                
                # Save to disk
                dataset_path = self.raw_dir / f"{dataset_info['name'].replace('/', '_')}.json"
                dataset.to_json(dataset_path)
                downloaded_datasets[dataset_info["name"]] = dataset_path
                print(f"✅ Saved {len(dataset)} examples to {dataset_path}")
                
            except Exception as e:
                print(f"❌ Failed to download {dataset_info['name']}: {e}")
        
        return downloaded_datasets
    
    def collect_github_repos(self, repo_urls):
        """Clone and extract code from GitHub repositories"""
        print("📥 Collecting code from GitHub repositories...")
        
        repo_data = []
        repos_dir = self.raw_dir / "github_repos"
        repos_dir.mkdir(exist_ok=True)
        
        for repo_url in repo_urls:
            try:
                repo_name = repo_url.split("/")[-1].replace(".git", "")
                repo_path = repos_dir / repo_name
                
                if not repo_path.exists():
                    print(f"Cloning {repo_name}...")
                    subprocess.run(
                        ["git", "clone", repo_url, str(repo_path)],
                        check=True, capture_output=True
                    )
                
                # Extract Python files
                python_files = list(repo_path.rglob("*.py"))
                for py_file in python_files:
                    try:
                        with open(py_file, 'r', encoding='utf-8') as f:
                            content = f.read()
                            if len(content) > 100:  # Skip very small files
                                repo_data.append({
                                    "code": content,
                                    "path": str(py_file.relative_to(repo_path)),
                                    "repo": repo_name,
                                    "language": "python"
                                })
                    except Exception as e:
                        continue
                
                print(f"✅ Extracted {len([f for f in python_files])} Python files from {repo_name}")
                
            except Exception as e:
                print(f"❌ Failed to process {repo_url}: {e}")
        
        # Save collected data
        if repo_data:
            repo_dataset_path = self.raw_dir / "github_repos_data.json"
            with open(repo_dataset_path, 'w') as f:
                json.dump(repo_data, f, indent=2)
            print(f"✅ Saved {len(repo_data)} code samples from repositories")
        
        return repo_data
    
    def create_instruction_dataset(self):
        """Create instruction-following dataset for code generation"""
        print("📝 Creating instruction dataset...")
        
        # Sample instruction-code pairs
        instruction_data = [
            {
                "instruction": "Write a Python function to calculate the factorial of a number",
                "code": """def factorial(n):
    if n == 0 or n == 1:
        return 1
    else:
        return n * factorial(n - 1)"""
            },
            {
                "instruction": "Create a Python class for a simple calculator",
                "code": """class Calculator:
    def add(self, a, b):
        return a + b
    
    def subtract(self, a, b):
        return a - b
    
    def multiply(self, a, b):
        return a * b
    
    def divide(self, a, b):
        if b != 0:
            return a / b
        else:
            raise ValueError("Cannot divide by zero")"""
            },
            {
                "instruction": "Write a function to check if a string is a palindrome",
                "code": """def is_palindrome(s):
    s = s.lower().replace(' ', '')
    return s == s[::-1]"""
            }
        ]
        
        # Save instruction dataset
        instruction_path = self.raw_dir / "instruction_dataset.json"
        with open(instruction_path, 'w') as f:
            json.dump(instruction_data, f, indent=2)
        
        print(f"✅ Created instruction dataset with {len(instruction_data)} examples")
        return instruction_data
    
    def preprocess_data(self, tokenizer_name="microsoft/CodeBERT-base"):
        """Preprocess collected data for training"""
        print("🔄 Preprocessing data for training...")
        
        tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)
        
        # Load all collected data
        all_data = []
        
        # Load from JSON files in raw directory
        for json_file in self.raw_dir.glob("*.json"):
            try:
                with open(json_file, 'r') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        all_data.extend(data)
                    else:
                        all_data.append(data)
            except Exception as e:
                print(f"❌ Failed to load {json_file}: {e}")
        
        if not all_data:
            print("❌ No data found to preprocess")
            return None
        
        print(f"📊 Processing {len(all_data)} code samples...")
        
        # Prepare data for different training tasks
        processed_data = {
            "code_completion": [],
            "code_generation": [],
            "code_understanding": []
        }
        
        for item in tqdm(all_data, desc="Processing samples"):
            try:
                if "code" in item:
                    code = item["code"]
                    
                    # Code completion task (predict next tokens)
                    if len(code) > 200:
                        mid_point = len(code) // 2
                        processed_data["code_completion"].append({
                            "input": code[:mid_point],
                            "target": code[mid_point:],
                            "task": "completion"
                        })
                    
                    # Code generation task (from instruction)
                    if "instruction" in item:
                        processed_data["code_generation"].append({
                            "input": item["instruction"],
                            "target": code,
                            "task": "generation"
                        })
                    
                    # Code understanding task (summarization)
                    if len(code) > 100:
                        # Simple heuristic for creating summaries
                        summary = f"This code defines functionality in {item.get('language', 'Python')}"
                        processed_data["code_understanding"].append({
                            "input": code,
                            "target": summary,
                            "task": "understanding"
                        })
                
            except Exception as e:
                continue
        
        # Save processed datasets
        for task, data in processed_data.items():
            if data:
                # Create train/validation split
                split_idx = int(len(data) * 0.8)
                train_data = data[:split_idx]
                val_data = data[split_idx:]
                
                # Save as Hugging Face datasets
                train_dataset = Dataset.from_list(train_data)
                val_dataset = Dataset.from_list(val_data)
                
                dataset_dict = DatasetDict({
                    "train": train_dataset,
                    "validation": val_dataset
                })
                
                # Save to disk
                dataset_path = self.datasets_dir / task
                dataset_dict.save_to_disk(dataset_path)
                
                print(f"✅ Saved {task} dataset: {len(train_data)} train, {len(val_data)} val")
        
        return processed_data

def main():
    """Main data collection function"""
    print("🗃️  Starting Data Collection for Coding Model Training")
    print("=" * 60)
    
    collector = CodeDataCollector()
    
    # Step 1: Download public datasets
    public_datasets = collector.download_public_datasets()
    
    # Step 2: Create instruction dataset
    instruction_data = collector.create_instruction_dataset()
    
    # Step 3: Optionally collect from GitHub repos
    # Uncomment and add your favorite repos
    # github_repos = [
    #     "https://github.com/python/cpython.git",
    #     "https://github.com/django/django.git"
    # ]
    # repo_data = collector.collect_github_repos(github_repos)
    
    # Step 4: Preprocess all data
    processed_data = collector.preprocess_data()
    
    if processed_data:
        print("\n🎉 Data collection and preprocessing completed!")
        print("\nDatasets created:")
        for task, data in processed_data.items():
            if data:
                print(f"  - {task}: {len(data)} samples")
        
        print("\nNext steps:")
        print("1. Review the datasets in ./data/datasets/")
        print("2. Adjust preprocessing if needed")
        print("3. Start training with: python train_model.py")
    else:
        print("❌ Data collection failed")

if __name__ == "__main__":
    main()
