#!/usr/bin/env python3
"""
Test script for trained coding models
Evaluates model performance on various coding tasks
"""

import torch
from transformers import <PERSON>Tokenizer, AutoModelForCausalLM
import json
from pathlib import Path
import time
from typing import List, Dict

class CodingModelTester:
    def __init__(self, model_path: str = "./models/checkpoints"):
        self.model_path = Path(model_path)
        self.tokenizer = None
        self.model = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
    def load_model(self):
        """Load the trained model and tokenizer"""
        print(f"🔄 Loading model from {self.model_path}...")
        
        try:
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)
            self.model = AutoModelForCausalLM.from_pretrained(self.model_path)
            self.model.to(self.device)
            self.model.eval()
            
            print(f"✅ Model loaded successfully on {self.device}")
            print(f"   Model parameters: {self.model.num_parameters():,}")
            
        except Exception as e:
            print(f"❌ Failed to load model: {e}")
            print("   Make sure you've trained a model first with train_model.py")
            return False
        
        return True
    
    def generate_code(self, prompt: str, max_length: int = 200, temperature: float = 0.7) -> str:
        """Generate code from a prompt"""
        if not self.model or not self.tokenizer:
            return "Model not loaded"
        
        # Tokenize input
        inputs = self.tokenizer.encode(prompt, return_tensors="pt").to(self.device)
        
        # Generate
        with torch.no_grad():
            outputs = self.model.generate(
                inputs,
                max_length=max_length,
                temperature=temperature,
                do_sample=True,
                top_p=0.9,
                top_k=50,
                pad_token_id=self.tokenizer.eos_token_id,
                num_return_sequences=1
            )
        
        # Decode output
        generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # Remove the input prompt from output
        if generated_text.startswith(prompt):
            generated_text = generated_text[len(prompt):]
        
        return generated_text.strip()
    
    def test_code_completion(self):
        """Test code completion capabilities"""
        print("\n🧪 Testing Code Completion...")
        
        test_cases = [
            {
                "prompt": "def fibonacci(n):",
                "expected_keywords": ["if", "return", "fibonacci"]
            },
            {
                "prompt": "class Calculator:",
                "expected_keywords": ["def", "__init__", "self"]
            },
            {
                "prompt": "import pandas as pd\ndef load_data(file_path):",
                "expected_keywords": ["pd.read", "return", "csv"]
            },
            {
                "prompt": "def binary_search(arr, target):",
                "expected_keywords": ["left", "right", "mid", "while"]
            }
        ]
        
        results = []
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n  Test {i}: {test_case['prompt']}")
            
            start_time = time.time()
            generated = self.generate_code(test_case["prompt"])
            generation_time = time.time() - start_time
            
            print(f"  Generated ({generation_time:.2f}s):")
            print(f"  {generated[:200]}...")
            
            # Check if expected keywords are present
            keywords_found = sum(1 for keyword in test_case["expected_keywords"] 
                               if keyword.lower() in generated.lower())
            keyword_score = keywords_found / len(test_case["expected_keywords"])
            
            results.append({
                "prompt": test_case["prompt"],
                "generated": generated,
                "keyword_score": keyword_score,
                "generation_time": generation_time
            })
            
            print(f"  Keyword Score: {keyword_score:.2f}")
        
        avg_keyword_score = sum(r["keyword_score"] for r in results) / len(results)
        avg_time = sum(r["generation_time"] for r in results) / len(results)
        
        print(f"\n📊 Code Completion Results:")
        print(f"   Average Keyword Score: {avg_keyword_score:.2f}")
        print(f"   Average Generation Time: {avg_time:.2f}s")
        
        return results
    
    def test_code_generation(self):
        """Test code generation from natural language"""
        print("\n🧪 Testing Code Generation...")
        
        test_cases = [
            "Write a function to reverse a string",
            "Create a class for a simple bank account",
            "Write a function to find the maximum element in a list",
            "Create a decorator that measures execution time"
        ]
        
        results = []
        for i, instruction in enumerate(test_cases, 1):
            print(f"\n  Test {i}: {instruction}")
            
            start_time = time.time()
            generated = self.generate_code(instruction, max_length=300)
            generation_time = time.time() - start_time
            
            print(f"  Generated ({generation_time:.2f}s):")
            print(f"  {generated[:300]}...")
            
            # Simple quality checks
            has_function = "def " in generated
            has_class = "class " in generated or has_function
            has_return = "return" in generated
            proper_indentation = "    " in generated  # Check for indentation
            
            quality_score = sum([has_function or has_class, has_return, proper_indentation]) / 3
            
            results.append({
                "instruction": instruction,
                "generated": generated,
                "quality_score": quality_score,
                "generation_time": generation_time
            })
            
            print(f"  Quality Score: {quality_score:.2f}")
        
        avg_quality_score = sum(r["quality_score"] for r in results) / len(results)
        avg_time = sum(r["generation_time"] for r in results) / len(results)
        
        print(f"\n📊 Code Generation Results:")
        print(f"   Average Quality Score: {avg_quality_score:.2f}")
        print(f"   Average Generation Time: {avg_time:.2f}s")
        
        return results
    
    def test_interactive_session(self):
        """Interactive testing session"""
        print("\n💬 Interactive Testing Session")
        print("Enter prompts to test your model (type 'quit' to exit):")
        
        while True:
            try:
                prompt = input("\n🤖 Enter prompt: ").strip()
                
                if prompt.lower() in ['quit', 'exit', 'q']:
                    break
                
                if not prompt:
                    continue
                
                print("🔄 Generating...")
                start_time = time.time()
                generated = self.generate_code(prompt, max_length=400)
                generation_time = time.time() - start_time
                
                print(f"\n📝 Generated code ({generation_time:.2f}s):")
                print("-" * 50)
                print(generated)
                print("-" * 50)
                
            except KeyboardInterrupt:
                break
        
        print("\n👋 Interactive session ended")
    
    def benchmark_performance(self):
        """Benchmark model performance"""
        print("\n⚡ Benchmarking Performance...")
        
        test_prompts = [
            "def quicksort(arr):",
            "class LinkedList:",
            "import numpy as np\ndef matrix_multiply(a, b):",
        ] * 10  # Repeat for more samples
        
        times = []
        for prompt in test_prompts:
            start_time = time.time()
            _ = self.generate_code(prompt, max_length=100)
            times.append(time.time() - start_time)
        
        avg_time = sum(times) / len(times)
        min_time = min(times)
        max_time = max(times)
        
        print(f"📊 Performance Benchmark:")
        print(f"   Average time: {avg_time:.3f}s")
        print(f"   Min time: {min_time:.3f}s")
        print(f"   Max time: {max_time:.3f}s")
        print(f"   Throughput: {1/avg_time:.1f} generations/second")
        
        return {
            "avg_time": avg_time,
            "min_time": min_time,
            "max_time": max_time,
            "throughput": 1/avg_time
        }
    
    def save_test_results(self, results: Dict):
        """Save test results to file"""
        results_path = Path("./results/test_results.json")
        results_path.parent.mkdir(exist_ok=True)
        
        with open(results_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"💾 Test results saved to {results_path}")

def main():
    """Main testing function"""
    print("🧪 Testing Trained Coding Model")
    print("=" * 40)
    
    # Initialize tester
    tester = CodingModelTester()
    
    # Load model
    if not tester.load_model():
        return
    
    # Run tests
    all_results = {}
    
    try:
        # Test code completion
        completion_results = tester.test_code_completion()
        all_results["code_completion"] = completion_results
        
        # Test code generation
        generation_results = tester.test_code_generation()
        all_results["code_generation"] = generation_results
        
        # Benchmark performance
        performance_results = tester.benchmark_performance()
        all_results["performance"] = performance_results
        
        # Save results
        tester.save_test_results(all_results)
        
        print("\n🎉 All tests completed!")
        print("\nWould you like to try interactive testing? (y/n)")
        
        if input().lower().startswith('y'):
            tester.test_interactive_session()
    
    except KeyboardInterrupt:
        print("\n\n⏹️  Testing interrupted by user")
    
    except Exception as e:
        print(f"\n❌ Testing failed: {e}")

if __name__ == "__main__":
    main()
