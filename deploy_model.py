#!/usr/bin/env python3
"""
Deployment script for trained coding models
Sets up API endpoints and integration tools
"""

import os
import torch
from pathlib import Path
from flask import Flask, request, jsonify
from transformers import AutoTokenizer, AutoModelForCausalLM
import argparse
import threading
import time

class CodingModelAPI:
    def __init__(self, model_path: str = "./models/checkpoints"):
        self.model_path = Path(model_path)
        self.tokenizer = None
        self.model = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.app = Flask(__name__)
        self.setup_routes()
        
    def load_model(self):
        """Load the trained model"""
        print(f"🔄 Loading model from {self.model_path}...")
        
        try:
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)
            self.model = AutoModelForCausalLM.from_pretrained(self.model_path)
            self.model.to(self.device)
            self.model.eval()
            
            print(f"✅ Model loaded on {self.device}")
            return True
            
        except Exception as e:
            print(f"❌ Failed to load model: {e}")
            return False
    
    def generate_code(self, prompt: str, max_length: int = 200, temperature: float = 0.7):
        """Generate code from prompt"""
        if not self.model or not self.tokenizer:
            return {"error": "Model not loaded"}
        
        try:
            # Tokenize
            inputs = self.tokenizer.encode(prompt, return_tensors="pt").to(self.device)
            
            # Generate
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    max_length=max_length,
                    temperature=temperature,
                    do_sample=True,
                    top_p=0.9,
                    top_k=50,
                    pad_token_id=self.tokenizer.eos_token_id,
                    num_return_sequences=1
                )
            
            # Decode
            generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Remove prompt from output
            if generated_text.startswith(prompt):
                generated_text = generated_text[len(prompt):]
            
            return {
                "generated_code": generated_text.strip(),
                "prompt": prompt,
                "success": True
            }
            
        except Exception as e:
            return {"error": str(e), "success": False}
    
    def setup_routes(self):
        """Setup Flask API routes"""
        
        @self.app.route('/health', methods=['GET'])
        def health_check():
            """Health check endpoint"""
            return jsonify({
                "status": "healthy",
                "model_loaded": self.model is not None,
                "device": str(self.device)
            })
        
        @self.app.route('/generate', methods=['POST'])
        def generate():
            """Code generation endpoint"""
            try:
                data = request.get_json()
                
                if not data or 'prompt' not in data:
                    return jsonify({"error": "Missing 'prompt' in request"}), 400
                
                prompt = data['prompt']
                max_length = data.get('max_length', 200)
                temperature = data.get('temperature', 0.7)
                
                result = self.generate_code(prompt, max_length, temperature)
                
                if result.get('success', False):
                    return jsonify(result)
                else:
                    return jsonify(result), 500
                    
            except Exception as e:
                return jsonify({"error": str(e)}), 500
        
        @self.app.route('/complete', methods=['POST'])
        def complete():
            """Code completion endpoint"""
            try:
                data = request.get_json()
                
                if not data or 'code' not in data:
                    return jsonify({"error": "Missing 'code' in request"}), 400
                
                code = data['code']
                max_length = data.get('max_length', 100)
                
                result = self.generate_code(code, max_length, temperature=0.3)
                
                if result.get('success', False):
                    return jsonify({
                        "completion": result['generated_code'],
                        "original_code": code,
                        "success": True
                    })
                else:
                    return jsonify(result), 500
                    
            except Exception as e:
                return jsonify({"error": str(e)}), 500
        
        @self.app.route('/explain', methods=['POST'])
        def explain():
            """Code explanation endpoint"""
            try:
                data = request.get_json()
                
                if not data or 'code' not in data:
                    return jsonify({"error": "Missing 'code' in request"}), 400
                
                code = data['code']
                prompt = f"Explain this code:\n{code}\nExplanation:"
                
                result = self.generate_code(prompt, max_length=300, temperature=0.5)
                
                if result.get('success', False):
                    return jsonify({
                        "explanation": result['generated_code'],
                        "original_code": code,
                        "success": True
                    })
                else:
                    return jsonify(result), 500
                    
            except Exception as e:
                return jsonify({"error": str(e)}), 500
    
    def run_server(self, host='localhost', port=5000, debug=False):
        """Run the Flask server"""
        print(f"🚀 Starting API server on http://{host}:{port}")
        self.app.run(host=host, port=port, debug=debug, threaded=True)

def create_cli_tool():
    """Create a command-line interface tool"""
    cli_script = '''#!/usr/bin/env python3
"""
Command-line interface for the coding model
"""

import requests
import argparse
import json

def call_api(endpoint, data, base_url="http://localhost:5000"):
    """Call the API endpoint"""
    try:
        response = requests.post(f"{base_url}/{endpoint}", json=data)
        return response.json()
    except Exception as e:
        return {"error": str(e)}

def main():
    parser = argparse.ArgumentParser(description="Coding Model CLI")
    parser.add_argument("command", choices=["generate", "complete", "explain"])
    parser.add_argument("--prompt", "-p", help="Prompt for generation")
    parser.add_argument("--code", "-c", help="Code for completion/explanation")
    parser.add_argument("--max-length", "-m", type=int, default=200, help="Max generation length")
    parser.add_argument("--temperature", "-t", type=float, default=0.7, help="Generation temperature")
    parser.add_argument("--url", default="http://localhost:5000", help="API base URL")
    
    args = parser.parse_args()
    
    if args.command == "generate":
        if not args.prompt:
            print("Error: --prompt required for generate command")
            return
        
        data = {
            "prompt": args.prompt,
            "max_length": args.max_length,
            "temperature": args.temperature
        }
        result = call_api("generate", data, args.url)
        
    elif args.command == "complete":
        if not args.code:
            print("Error: --code required for complete command")
            return
        
        data = {
            "code": args.code,
            "max_length": args.max_length
        }
        result = call_api("complete", data, args.url)
        
    elif args.command == "explain":
        if not args.code:
            print("Error: --code required for explain command")
            return
        
        data = {"code": args.code}
        result = call_api("explain", data, args.url)
    
    print(json.dumps(result, indent=2))

if __name__ == "__main__":
    main()
'''
    
    with open("coding_model_cli.py", "w") as f:
        f.write(cli_script)
    
    print("✅ Created CLI tool: coding_model_cli.py")

def create_integration_examples():
    """Create integration examples for popular editors"""
    
    # VS Code extension example
    vscode_example = '''// VS Code Extension Example
// This would go in a VS Code extension

const vscode = require('vscode');
const axios = require('axios');

async function generateCode(prompt) {
    try {
        const response = await axios.post('http://localhost:5000/generate', {
            prompt: prompt,
            max_length: 200,
            temperature: 0.7
        });
        
        return response.data.generated_code;
    } catch (error) {
        vscode.window.showErrorMessage('Code generation failed: ' + error.message);
        return null;
    }
}

function activate(context) {
    let disposable = vscode.commands.registerCommand('extension.generateCode', async () => {
        const prompt = await vscode.window.showInputBox({
            prompt: 'Enter code generation prompt'
        });
        
        if (prompt) {
            const code = await generateCode(prompt);
            if (code) {
                const editor = vscode.window.activeTextEditor;
                if (editor) {
                    editor.edit(editBuilder => {
                        editBuilder.insert(editor.selection.active, code);
                    });
                }
            }
        }
    });
    
    context.subscriptions.push(disposable);
}

exports.activate = activate;
'''
    
    with open("examples/vscode_integration.js", "w") as f:
        os.makedirs("examples", exist_ok=True)
        f.write(vscode_example)
    
    print("✅ Created VS Code integration example")

def main():
    """Main deployment function"""
    parser = argparse.ArgumentParser(description="Deploy Coding Model")
    parser.add_argument("--model-path", default="./models/checkpoints", help="Path to trained model")
    parser.add_argument("--host", default="localhost", help="Server host")
    parser.add_argument("--port", type=int, default=5000, help="Server port")
    parser.add_argument("--create-cli", action="store_true", help="Create CLI tool")
    parser.add_argument("--create-examples", action="store_true", help="Create integration examples")
    
    args = parser.parse_args()
    
    print("🚀 Deploying Coding Model")
    print("=" * 30)
    
    # Create additional tools if requested
    if args.create_cli:
        create_cli_tool()
    
    if args.create_examples:
        create_integration_examples()
    
    # Initialize API
    api = CodingModelAPI(args.model_path)
    
    # Load model
    if not api.load_model():
        print("❌ Failed to load model. Exiting.")
        return
    
    print("\n📡 API Endpoints:")
    print(f"   Health Check: http://{args.host}:{args.port}/health")
    print(f"   Generate Code: http://{args.host}:{args.port}/generate")
    print(f"   Complete Code: http://{args.host}:{args.port}/complete")
    print(f"   Explain Code: http://{args.host}:{args.port}/explain")
    
    print("\n💡 Usage Examples:")
    print("   curl -X POST http://localhost:5000/generate -H 'Content-Type: application/json' -d '{\"prompt\": \"def fibonacci(n):\"}'")
    
    # Start server
    try:
        api.run_server(args.host, args.port)
    except KeyboardInterrupt:
        print("\n⏹️  Server stopped by user")

if __name__ == "__main__":
    main()
