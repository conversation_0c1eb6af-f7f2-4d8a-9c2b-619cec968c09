#!/usr/bin/env python3
"""
Setup script for coding model training environment
This script installs all necessary dependencies and sets up the environment
"""

import subprocess
import sys
import os
import platform
import torch

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def check_gpu():
    """Check GPU availability and CUDA support"""
    print("\n🔍 Checking GPU and CUDA support...")
    
    if torch.cuda.is_available():
        gpu_count = torch.cuda.device_count()
        gpu_name = torch.cuda.get_device_name(0)
        cuda_version = torch.version.cuda
        print(f"✅ CUDA available: {cuda_version}")
        print(f"✅ GPU count: {gpu_count}")
        print(f"✅ GPU 0: {gpu_name}")
        
        # Check VRAM
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        print(f"✅ GPU Memory: {gpu_memory:.1f} GB")
        
        if gpu_memory < 8:
            print("⚠️  Warning: Less than 8GB VRAM. Consider fine-tuning smaller models.")
        elif gpu_memory >= 24:
            print("🚀 Excellent! You can train larger models.")
        
        return True
    else:
        print("❌ CUDA not available. Training will be very slow on CPU.")
        return False

def install_dependencies():
    """Install required Python packages"""
    packages = [
        "torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118",
        "transformers[torch]",
        "datasets",
        "accelerate",
        "evaluate",
        "wandb",
        "tensorboard",
        "scikit-learn",
        "pandas",
        "numpy",
        "tqdm",
        "matplotlib",
        "seaborn",
        "jupyter",
        "ipywidgets"
    ]
    
    print("\n📦 Installing Python packages...")
    for package in packages:
        if not run_command(f"pip install {package}", f"Installing {package.split()[0]}"):
            return False
    
    return True

def setup_directories():
    """Create necessary directories for training"""
    directories = [
        "data/raw",
        "data/processed", 
        "data/datasets",
        "models/checkpoints",
        "models/final",
        "logs",
        "scripts",
        "configs",
        "results"
    ]
    
    print("\n📁 Creating directory structure...")
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ Created: {directory}")

def create_config_files():
    """Create basic configuration files"""
    
    # Training config template
    training_config = """# Training Configuration Template
model_name: "microsoft/CodeBERT-base"
output_dir: "./models/checkpoints"
data_dir: "./data/processed"

# Training parameters
num_train_epochs: 3
per_device_train_batch_size: 8
per_device_eval_batch_size: 8
learning_rate: 2e-5
weight_decay: 0.01
warmup_steps: 500
logging_steps: 100
save_steps: 1000
eval_steps: 1000

# Model parameters
max_length: 512
gradient_accumulation_steps: 1
fp16: true
dataloader_num_workers: 4

# Evaluation
evaluation_strategy: "steps"
save_strategy: "steps"
load_best_model_at_end: true
metric_for_best_model: "eval_loss"
"""
    
    with open("configs/training_config.yaml", "w") as f:
        f.write(training_config)
    
    # Requirements file
    requirements = """torch>=2.0.0
transformers>=4.30.0
datasets>=2.12.0
accelerate>=0.20.0
evaluate>=0.4.0
wandb>=0.15.0
tensorboard>=2.13.0
scikit-learn>=1.3.0
pandas>=2.0.0
numpy>=1.24.0
tqdm>=4.65.0
matplotlib>=3.7.0
seaborn>=0.12.0
jupyter>=1.0.0
ipywidgets>=8.0.0
"""
    
    with open("requirements.txt", "w") as f:
        f.write(requirements)
    
    print("✅ Created configuration files")

def main():
    """Main setup function"""
    print("🚀 Setting up Coding Model Training Environment")
    print("=" * 50)
    
    # Check Python version
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print("❌ Python 3.8+ required")
        return False
    
    print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # Check platform
    print(f"✅ Platform: {platform.system()} {platform.release()}")
    
    # Setup steps
    steps = [
        (check_gpu, "GPU Check"),
        (install_dependencies, "Package Installation"),
        (setup_directories, "Directory Setup"),
        (create_config_files, "Configuration Files")
    ]
    
    for step_func, step_name in steps:
        if not step_func():
            print(f"\n❌ Setup failed at: {step_name}")
            return False
    
    print("\n🎉 Environment setup completed successfully!")
    print("\nNext steps:")
    print("1. Run: python data_collection.py")
    print("2. Prepare your training data")
    print("3. Start training with: python train_model.py")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
