# AI Coding Assistant Setup Script
# This script sets up Ollama and downloads a coding-focused AI model

Write-Host "Setting up AI Coding Assistant..." -ForegroundColor Green

# Check if Ollama is already installed
try {
    $ollamaVersion = ollama --version 2>$null
    if ($ollamaVersion) {
        Write-Host "Ollama is already installed: $ollamaVersion" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Ollama not found. Installing..." -ForegroundColor Blue
    
    # Download Ollama for Windows
    $ollamaUrl = "https://ollama.com/download/OllamaSetup.exe"
    $ollamaInstaller = "OllamaSetup.exe"
    
    Write-Host "Downloading Ollama installer..." -ForegroundColor Blue
    try {
        Invoke-WebRequest -Uri $ollamaUrl -OutFile $ollamaInstaller -UseBasicParsing
        Write-Host "Download completed. Running installer..." -ForegroundColor Blue
        
        # Run the installer
        Start-Process -FilePath $ollamaInstaller -Wait
        
        # Clean up installer
        Remove-Item $ollamaInstaller -Force
        
        Write-Host "Ollama installation completed!" -ForegroundColor Green
        Write-Host "Please restart your terminal and run this script again." -ForegroundColor Yellow
        exit
    } catch {
        Write-Host "Failed to download Ollama. Please visit https://ollama.com/download and install manually." -ForegroundColor Red
        exit 1
    }
}

# Check if Ollama service is running
Write-Host "Checking Ollama service..." -ForegroundColor Blue
try {
    $ollamaList = ollama list 2>$null
    Write-Host "Ollama service is running!" -ForegroundColor Green
} catch {
    Write-Host "Starting Ollama service..." -ForegroundColor Blue
    Start-Process "ollama" -ArgumentList "serve" -WindowStyle Hidden
    Start-Sleep -Seconds 5
}

# Install coding models
Write-Host "Installing coding models..." -ForegroundColor Blue

$models = @(
    @{name="deepseek-coder:6.7b"; description="Deepseek Coder - Excellent for code generation and completion"},
    @{name="codellama:7b"; description="Code Llama - Meta's specialized coding model"}
)

foreach ($model in $models) {
    Write-Host "Installing $($model.name) - $($model.description)" -ForegroundColor Blue
    try {
        ollama pull $model.name
        Write-Host "Successfully installed $($model.name)" -ForegroundColor Green
    } catch {
        Write-Host "Failed to install $($model.name)" -ForegroundColor Red
    }
}

Write-Host "`nSetup completed!" -ForegroundColor Green
Write-Host "Available commands:" -ForegroundColor Yellow
Write-Host "  ollama list                    - List installed models" -ForegroundColor White
Write-Host "  ollama run deepseek-coder:6.7b - Start interactive coding session" -ForegroundColor White
Write-Host "  ollama run codellama:7b        - Start Code Llama session" -ForegroundColor White
