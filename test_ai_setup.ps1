# Test AI Setup Script
# Verifies that the AI coding assistant is working correctly

Write-Host "Testing AI Coding Assistant Setup..." -ForegroundColor Green
Write-Host ""

# Test 1: Check if Ollama is installed
Write-Host "1. Testing Ollama installation..." -ForegroundColor Blue
try {
    $ollamaVersion = ollama --version 2>$null
    if ($ollamaVersion) {
        Write-Host "   ✓ Ollama is installed: $ollamaVersion" -ForegroundColor Green
    } else {
        Write-Host "   ✗ Ollama not found" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "   ✗ Ollama not found or not working" -ForegroundColor Red
    exit 1
}

# Test 2: Check if models are installed
Write-Host "2. Testing installed models..." -ForegroundColor Blue
try {
    $models = ollama list 2>$null
    if ($models -match "deepseek-coder") {
        Write-Host "   ✓ Deepseek Coder model found" -ForegroundColor Green
    } else {
        Write-Host "   ⚠ Deepseek Coder model not found" -ForegroundColor Yellow
    }
    
    if ($models -match "codellama") {
        Write-Host "   ✓ Code Llama model found" -ForegroundColor Green
    } else {
        Write-Host "   ⚠ Code Llama model not found" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   ✗ Could not list models" -ForegroundColor Red
}

# Test 3: Test AI helper script
Write-Host "3. Testing AI helper script..." -ForegroundColor Blue
if (Test-Path "ai_code_helper.ps1") {
    Write-Host "   ✓ AI helper script found" -ForegroundColor Green
} else {
    Write-Host "   ✗ AI helper script missing" -ForegroundColor Red
}

if (Test-Path "ai.bat") {
    Write-Host "   ✓ AI batch file found" -ForegroundColor Green
} else {
    Write-Host "   ✗ AI batch file missing" -ForegroundColor Red
}

# Test 4: Simple AI interaction test
Write-Host "4. Testing AI interaction..." -ForegroundColor Blue
try {
    # Create a simple test prompt
    $testPrompt = "Write a simple hello world function in Python. Keep it very brief."
    Write-Host "   Sending test prompt to AI..." -ForegroundColor Blue
    
    # Try to get a response from the AI (with timeout)
    $response = ollama run deepseek-coder:6.7b $testPrompt --timeout 30s 2>$null
    
    if ($response -and $response.Length -gt 10) {
        Write-Host "   ✓ AI responded successfully" -ForegroundColor Green
        Write-Host "   Sample response: $($response.Substring(0, [Math]::Min(100, $response.Length)))..." -ForegroundColor Cyan
    } else {
        Write-Host "   ⚠ AI response was empty or too short" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   ⚠ Could not test AI interaction (model may still be downloading)" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Setup test completed!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. If models are missing, run: .\setup_ai_coding_assistant.ps1" -ForegroundColor White
Write-Host "2. Try the AI helper: .\ai.bat help" -ForegroundColor White
Write-Host "3. Start coding with AI: .\ai.bat chat" -ForegroundColor White
